# Dashboard Auto-Refresh Fix

## Masalah
Website mengalami refresh otomatis ketika user mengklik di luar website kemudian kembali ke website. Hal ini disebabkan oleh event listener `window.focus` yang secara otomatis memuat ulang data dashboard setiap kali window mendapat fokus kembali.

## Solusi yang Diterapkan

### 1. Debounce Mechanism
- Menambahkan mekanisme debounce dengan threshold 30 detik
- Auto-refresh hanya terjadi jika user benar-benar meninggalkan website selama lebih dari 30 detik
- Mencegah refresh yang tidak perlu saat user hanya mengklik di luar sebentar

### 2. User Control Toggle
- Menambahkan toggle button di header dashboard untuk mengontrol auto-refresh
- User dapat mengaktifkan/menonaktifkan fitur auto-refresh sesuai preferensi
- Pengaturan tersimpan di localStorage dan persisten antar session

### 3. Enhanced Logging
- Menambahkan logging yang lebih detail untuk debugging
- Tracking waktu blur/focus untuk analisis behavior

## Perubahan File

### `dashboard.tsx`
- **<PERSON><PERSON> 79-86**: Menambahkan state `autoRefreshEnabled` dengan localStorage persistence
- **Baris 249-291**: Memperbarui logic focus handler dengan debounce dan user control
- **Baris 293-299**: Menambahkan useEffect untuk menyimpan preferensi ke localStorage
- **Baris 307-318**: Menambahkan fungsi `handleAutoRefreshToggle` dengan feedback

### `components/dashboard/header.tsx`
- **Baris 13**: Menambahkan import `RefreshCw` icon
- **Baris 22-26**: Menambahkan props `autoRefreshEnabled` dan `onAutoRefreshToggle`
- **Baris 79-89**: Menambahkan toggle button untuk auto-refresh dengan visual feedback

## Cara Kerja

### Auto-Refresh Logic
1. Ketika window kehilangan fokus (`blur`), waktu dicatat
2. Ketika window mendapat fokus kembali (`focus`), dihitung selisih waktu
3. Jika selisih waktu > 30 detik DAN auto-refresh enabled, maka data dimuat ulang
4. Jika tidak, refresh diabaikan

### User Control
1. Toggle button di header menampilkan status auto-refresh
2. Button berwarna biru dan beranimasi spin jika enabled
3. Button abu-abu jika disabled
4. Preferensi tersimpan otomatis di localStorage

## Benefits
- ✅ Menghilangkan refresh yang mengganggu saat mengklik di luar sebentar
- ✅ Tetap mempertahankan auto-refresh untuk user yang benar-benar meninggalkan website
- ✅ Memberikan kontrol penuh kepada user
- ✅ Pengaturan persisten antar session
- ✅ Visual feedback yang jelas
- ✅ Backward compatible - tidak merusak fungsionalitas existing

## Testing
Untuk menguji fix ini:
1. Buka dashboard
2. Klik di luar website sebentar (< 30 detik) - tidak ada refresh
3. Klik di luar website lama (> 30 detik) - ada refresh (jika enabled)
4. Toggle auto-refresh button - pengaturan tersimpan
5. Refresh browser - pengaturan tetap tersimpan
